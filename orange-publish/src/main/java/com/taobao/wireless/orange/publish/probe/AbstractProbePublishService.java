package com.taobao.wireless.orange.publish.probe;

import com.alibaba.fastjson.JSON;
import com.taobao.wireless.orange.common.constant.enums.AserverIndexType;
import com.taobao.wireless.orange.common.constant.enums.Emergent;
import com.taobao.wireless.orange.external.wmcc.WmccPublishService;
import com.taobao.wireless.orange.publish.probe.model.AserverProbe;
import com.taobao.wmcc.client.constants.BriefTaskStatus;
import com.taobao.wmcc.client.publish.ConfigPublishRequest;
import com.taobao.wmcc.client.publish.PublishTaskInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.Collections;
import java.util.Map;

/**
 * 探针发布服务抽象类
 * 提取 Switch 和 Text 探针发布的共同流程
 *
 * <AUTHOR>
 */
@Slf4j(topic = "probe")
public abstract class AbstractProbePublishService<T> {

    @Autowired
    protected WmccPublishService wmccPublishService;

    @Autowired
    protected ApplicationContext applicationContext;

    /**
     * 生成探针并发布
     */
    public void publish() {
        // 获取需要发布探针的应用列表
        Map<String, Emergent> candidateAppKey2Emergent = this.getCandidateAppKeys();

        // 若无任何候选应用，直接返回
        if (candidateAppKey2Emergent.isEmpty()) {
            log.info("{}_appKeys_is_empty, both emergent and non-emergent, skip publishing", getIndexType().name());
            return;
        }

        // 获取代理对象以确保事务生效
        var proxy = applicationContext.getBean(this.getClass());

        candidateAppKey2Emergent.forEach((appKey, emergent) -> {
            try {
                proxy.publishByAppKey(appKey, emergent);
            } catch (Exception e) {
                log.error("{}_publish_probe_error, emergent: {}, appKey: {}, error: {}", getIndexType().name(), emergent.name(), appKey, e.getMessage(), e);
            }
        });
    }

    /**
     * 发布单个应用的探针
     *
     * @param appKey
     * @param emergent
     */
    @Transactional
    public void publishByAppKey(String appKey, Emergent emergent) {
        Long publishingTaskId = this.getAppKeyPublishingTaskId(appKey, emergent);
        // 非紧急应用有正在发布的任务时，跳过发布
        if (Emergent.n.equals(emergent) && publishingTaskId != null) {
            log.info("{}_appKey_is_publishing, emergent: {}, appKey: {}, taskId: {}", getIndexType().name(), emergent.name(), appKey, publishingTaskId);
            return;
        }

        // 紧急应用有正在发布的任务时，取消任务
        if (Emergent.y.equals(emergent) && publishingTaskId != null) {
            wmccPublishService.cancelTask(publishingTaskId);
            this.clearCanceledProbeTaskId(appKey, publishingTaskId);
            log.info("{}_cancel_task_success, emergent: {}, appKey: {}, taskId: {}", getIndexType().name(), emergent.name(), appKey, publishingTaskId);
        }

        // 生成探针数据（只有要发布 wmcc 的任务才调用）
        T probe = this.generateProbeData(appKey);
        if (probe == null) {
            log.error("{}_probe_data_is_empty, emergent: {}, appKey: {}", getIndexType().name(), emergent.name(), appKey);
            return;
        }

        // 发布探针到 WMCC
        Long taskId = wmccPublishService.publishProbeToAserver(generateProbeWmccConfig(probe), emergent);
        log.info("{}_publish_probe_to_aserver_success, emergent: {}, appKey: {}, taskId: {}", getIndexType().name(), emergent.name(), appKey, taskId);

        // 更新探针任务ID
        this.updateProbeTaskId(probe, taskId);
        log.info("{}_update_probe_task_id_success, emergent: {}, appKey: {}, taskId: {}", getIndexType().name(), emergent.name(), appKey, taskId);
    }

    private Long getAppKeyPublishingTaskId(String appKey, Emergent emergent) {
        PublishTaskInfo taskInfo = wmccPublishService.getRunningPublishTaskInfo(appKey, getIndexType().getCode());
        boolean isPublishing = taskInfo != null &&
                (BriefTaskStatus.RUNNING.equals(taskInfo.getStatus()) ||
                        BriefTaskStatus.FAILURE.equals(taskInfo.getStatus()));
        log.info("{}_get_running_publish_task_info, emergent: {}, appKey: {}, isPublishing: {}", getIndexType().name(), emergent.name(), appKey, isPublishing);
        return isPublishing ? taskInfo.getTaskId() : null;
    }

    /**
     * 生成 AserverProbe 对象
     *
     * @param probe 探针数据
     * @return AserverProbe 对象
     */
    private AserverProbe generateAserverProbe(T probe) {
        return AserverProbe.builder()
                .appkey(getAppKey(probe))
                .indexType(getIndexType().getCode())
                .protocol("https")
                .probeVersion(getProbeVersion(probe))
                .versions(JSON.parseArray(getProbeContent(probe), AserverProbe.Version.class))
                .build();
    }

    /**
     * 生成探针 WMCC 配置
     *
     * @param probe 探针数据
     * @return WMCC 配置集合
     */
    private Collection<ConfigPublishRequest.Pair<String, String>> generateProbeWmccConfig(T probe) {
        String key = String.format("%s_%s", getAppKey(probe), getIndexType().getCode());
        AserverProbe aserverProbe = generateAserverProbe(probe);
        aserverProbe.setHost(getCdnDomain());
        String value = JSON.toJSONString(aserverProbe);
        return Collections.singletonList(ConfigPublishRequest.Pair.of(key, value));
    }

    /**
     * 获取候选的应用列表（未过滤发布状态）
     *
     * @return 候选应用列表
     */
    protected abstract Map<String, Emergent> getCandidateAppKeys();

    /**
     * 根据应用Key获取探针数据
     *
     * @param appKey 应用Key
     * @return 探针数据，如果没有数据则返回null
     */
    protected abstract T generateProbeData(String appKey);

    /**
     * 更新探针任务ID
     *
     * @param probe  探针数据
     * @param taskId 发布任务ID
     */
    protected abstract void updateProbeTaskId(T probe, Long taskId);

    /**
     * 更新被取消探针任务的任务ID为新任务的ID
     *
     * @param appKey         本次发布的应用
     * @param canceledTaskId 被取消的任务ID
     */
    protected abstract void clearCanceledProbeTaskId(String appKey, Long canceledTaskId);

    /**
     * 获取探针的应用Key
     *
     * @param probe 探针数据
     * @return 应用Key
     */
    protected abstract String getAppKey(T probe);

    /**
     * 获取探针版本
     *
     * @param probe 探针数据
     * @return 探针版本
     */
    protected abstract String getProbeVersion(T probe);

    /**
     * 获取探针内容
     *
     * @param probe 探针数据
     * @return 探针内容JSON字符串
     */
    protected abstract String getProbeContent(T probe);

    /**
     * 获取索引类型
     *
     * @return 索引类型
     */
    protected abstract AserverIndexType getIndexType();

    /**
     * 获取CDN域名
     *
     * @return CDN域名
     */
    protected abstract String getCdnDomain();
}
