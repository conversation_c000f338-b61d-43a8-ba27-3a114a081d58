<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.taobao.wireless.orange.text.dal.mapper.ProbeMapper">

    <select id="getAvailableProbesWithMaxTaskID" resultType="com.taobao.wireless.orange.text.dal.entity.ProbeTaskExt">
        SELECT probe.app_key, probe.metas, probe.index_version, probe.change_version, probe.publish_type, max_task_id
        FROM (SELECT app_key, metas, index_version, change_version, publish_type
              FROM orange_probe
              WHERE is_available = 'y' AND app_key = #{appKey}) probe
        LEFT JOIN (SELECT app_key, max(id) max_task_id
                   FROM orange_probe_task
                   WHERE app_key = #{appKey} AND is_available = 'n' LIMIT 1) probe_task
        ON probe.app_key = probe_task.app_key
    </select>

</mapper>
